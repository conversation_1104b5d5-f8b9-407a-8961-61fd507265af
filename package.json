{"name": "translation-app", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@chakra-ui/icons": "^2.2.1", "@chakra-ui/react": "^2.9.1", "@emotion/react": "^11.13.3", "@emotion/styled": "^11.13.0", "@google-cloud/translate": "^8.5.0", "axios": "^1.7.7", "framer-motion": "^11.9.0", "next": "14.2.14", "react": "^18", "react-dom": "^18", "uuid": "^10.0.0"}, "devDependencies": {"@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "@types/uuid": "^10.0.0", "eslint": "^8", "eslint-config-next": "14.2.14", "postcss": "^8", "tailwindcss": "^3.4.1", "typescript": "^5"}}