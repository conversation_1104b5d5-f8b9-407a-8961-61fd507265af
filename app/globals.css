@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --background: #ffffff;
  --foreground: #171717;
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;
  }
}

body {
  color: var(--foreground);
  background: var(--background);
  font-family: Arial, Helvetica, sans-serif;
}

@layer utilities {
  .text-balance {
    text-wrap: balance;
  }
}

/* 自定义滚动条样式 */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #2D3748;
}

::-webkit-scrollbar-thumb {
  background: #4A5568;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #718096;
}

/* 添加平滑滚动效果 */
html {
  scroll-behavior: smooth;
}

/* 为输入框添加焦点样式 */
input:focus, textarea:focus, select:focus {
  box-shadow: 0 0 0 3px rgba(66, 153, 225, 0.6);
  outline: none;
}

/* 为按钮添加悬停效果 */
button:hover {
  transform: translateY(-2px);
  transition: all 0.3s ease;
}

button:active {
  transform: translateY(1px);
}

/* 添加发光效果 */
button:focus {
  outline: none;
  box-shadow: 0 0 0 3px rgba(66, 153, 225, 0.6);
}

/* 添加页面过渡动画 */
.page-transition {
  animation: fadeIn 0.5s ease-in-out;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}