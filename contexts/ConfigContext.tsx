import { createContext, useContext, useState, ReactNode, useEffect } from "react";

// 定义各种服务的配置类型
export interface OpenAIConfig {
  apiKey: string;
  model: string;
  baseURL?: string;
}

export interface TongyiConfig {
  apiKey: string;
  model: string;
}

export interface DeepLConfig {
  apiKey: string;
  isPro: boolean; // 是否为专业版
}

export interface SiliconFlowConfig {
  apiKey: string;
  model: string;
}

export interface DeepSeekConfig {
  apiKey: string;
  model: string;
}

export interface GoogleConfig {
  projectId: string;
  keyFilename: string;
}

export interface ServiceConfigs {
  openai: OpenAIConfig;
  tongyi: TongyiConfig;
  deepl: DeepLConfig;
  siliconflow: SiliconFlowConfig;
  deepseek: DeepSeekConfig;
  google: GoogleConfig;
}

interface ConfigContextProps {
  configs: ServiceConfigs;
  updateConfig: <T extends keyof ServiceConfigs>(service: T, config: Partial<ServiceConfigs[T]>) => void;
  getConfig: <T extends keyof ServiceConfigs>(service: T) => ServiceConfigs[T];
  isConfigured: (service: keyof ServiceConfigs) => boolean;
  clearConfigs: () => void;
  exportConfigs: () => void;
  importConfigs: (configString: string) => void;
}

const ConfigContext = createContext<ConfigContextProps | undefined>(undefined);

interface ConfigProviderProps {
  children: ReactNode;
}

// 默认配置
const defaultConfigs: ServiceConfigs = {
  openai: {
    apiKey: "",
    model: "gpt-4o-mini",
    baseURL: "https://api.openai.com/v1"
  },
  tongyi: {
    apiKey: "",
    model: "qwen-turbo"
  },
  deepl: {
    apiKey: "",
    isPro: false
  },
  siliconflow: {
    apiKey: "",
    model: "Qwen/Qwen2.5-32B-Instruct"
  },
  deepseek: {
    apiKey: "",
    model: "deepseek-chat"
  },
  google: {
    projectId: "",
    keyFilename: ""
  }
};

export const ConfigProvider = ({ children }: ConfigProviderProps) => {
  const [configs, setConfigs] = useState<ServiceConfigs>(defaultConfigs);

  useEffect(() => {
    // 从 localStorage 加载配置
    const savedConfigs = localStorage.getItem('serviceConfigs');
    if (savedConfigs) {
      try {
        const parsed = JSON.parse(savedConfigs);
        setConfigs({ ...defaultConfigs, ...parsed });
      } catch (error) {
        console.error('Failed to parse saved configs:', error);
      }
    }
  }, []);

  useEffect(() => {
    // 保存配置到 localStorage
    localStorage.setItem('serviceConfigs', JSON.stringify(configs));
  }, [configs]);

  const updateConfig = <T extends keyof ServiceConfigs>(
    service: T, 
    config: Partial<ServiceConfigs[T]>
  ) => {
    setConfigs(prev => ({
      ...prev,
      [service]: { ...prev[service], ...config }
    }));
  };

  const getConfig = <T extends keyof ServiceConfigs>(service: T): ServiceConfigs[T] => {
    return configs[service];
  };

  const isConfigured = (service: keyof ServiceConfigs): boolean => {
    const config = configs[service];
    switch (service) {
      case 'openai':
        return !!(config as OpenAIConfig).apiKey;
      case 'tongyi':
        return !!(config as TongyiConfig).apiKey;
      case 'deepl':
        return !!(config as DeepLConfig).apiKey;
      case 'siliconflow':
        return !!(config as SiliconFlowConfig).apiKey;
      case 'deepseek':
        return !!(config as DeepSeekConfig).apiKey;
      case 'google':
        return !!(config as GoogleConfig).projectId && !!(config as GoogleConfig).keyFilename;
      default:
        return false;
    }
  };

  const clearConfigs = () => {
    setConfigs(defaultConfigs);
    localStorage.removeItem('serviceConfigs');
  };

  const exportConfigs = () => {
    const configString = JSON.stringify(configs, null, 2);
    const blob = new Blob([configString], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'service_configs.json';
    a.click();
    URL.revokeObjectURL(url);
  };

  const importConfigs = (configString: string) => {
    try {
      const imported = JSON.parse(configString);
      setConfigs({ ...defaultConfigs, ...imported });
    } catch (error) {
      console.error('Failed to import configs:', error);
      throw new Error('配置文件格式错误');
    }
  };

  return (
    <ConfigContext.Provider value={{
      configs,
      updateConfig,
      getConfig,
      isConfigured,
      clearConfigs,
      exportConfigs,
      importConfigs
    }}>
      {children}
    </ConfigContext.Provider>
  );
};

export const useConfigContext = () => {
  const context = useContext(ConfigContext);
  if (!context) {
    throw new Error("useConfigContext 必须在 ConfigProvider 内使用");
  }
  return context;
};
