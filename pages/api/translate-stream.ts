import type { NextApiRequest, NextApiResponse } from "next";
import axios from "axios";

// 流式翻译API端点
export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== "POST") {
    res.status(405).json({ error: "Method not allowed" });
    return;
  }

  const { text, sourceLang, targetLang, service, customAPIs, serviceConfigs } = req.body;

  if (!text || !targetLang || !service) {
    res.status(400).json({ error: "Missing parameters" });
    return;
  }

  // 设置SSE响应头
  res.writeHead(200, {
    'Content-Type': 'text/event-stream',
    'Cache-Control': 'no-cache',
    'Connection': 'keep-alive',
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Headers': 'Cache-Control',
  });

  try {
    let translatedText = "";

    if (service.startsWith("custom_")) {
      const customAPI = customAPIs.find((api: any) => api.id === service);
      if (customAPI) {
        translatedText = await translateWithCustomAPIStream(text, sourceLang, targetLang, customAPI, res);
      } else {
        throw new Error("未找到自定义API配置");
      }
    } else {
      switch (service) {
        case "openai":
          translatedText = await translateWithOpenAIStream(text, sourceLang, targetLang, serviceConfigs?.openai, res);
          break;
        case "tongyi":
          translatedText = await translateWithTongyiStream(text, sourceLang, targetLang, serviceConfigs?.tongyi, res);
          break;
        case "siliconflow":
          translatedText = await translateWithSiliconFlowStream(text, sourceLang, targetLang, serviceConfigs?.siliconflow, res);
          break;
        case "deepseek":
          translatedText = await translateWithDeepSeekStream(text, sourceLang, targetLang, serviceConfigs?.deepseek, res);
          break;
        default:
          // 对于不支持流式的服务，回退到普通模式
          const response = await axios.post(`${req.headers.origin}/api/translate`, {
            text, sourceLang, targetLang, service, customAPIs, serviceConfigs
          });
          translatedText = response.data.translatedText;

          // 模拟流式输出
          const words = translatedText.split(' ');
          for (let i = 0; i < words.length; i++) {
            const chunk = words.slice(0, i + 1).join(' ');
            res.write(`data: ${JSON.stringify({ chunk, done: false })}\n\n`);
            await new Promise(resolve => setTimeout(resolve, 50)); // 50ms延迟
          }
          break;
      }
    }

    // 发送完成信号
    res.write(`data: ${JSON.stringify({ chunk: translatedText, done: true })}\n\n`);
    res.end();
  } catch (error: any) {
    res.write(`data: ${JSON.stringify({ error: error.message || "翻译失败" })}\n\n`);
    res.end();
  }
}

// OpenAI 流式翻译
const translateWithOpenAIStream = async (
  text: string,
  source: string,
  target: string,
  config?: any,
  res?: NextApiResponse
): Promise<string> => {
  const apiKey = config?.apiKey || process.env.OPENAI_API_KEY;
  const model = config?.model || "gpt-4o-mini";
  const baseURL = config?.baseURL || "https://api.openai.com/v1";

  if (!apiKey) {
    throw new Error("OpenAI API 密钥未配置");
  }

  const data = {
    model: model,
    messages: [
      {
        role: "user",
        content: `请将以下文本从${source}翻译成${target}语言：${text}`,
      },
    ],
    stream: true,
  };

  const requestConfig = {
    method: 'post',
    url: `${baseURL}/chat/completions`,
    headers: {
      'Accept': 'text/event-stream',
      'Authorization': `Bearer ${apiKey}`,
      'Content-Type': 'application/json'
    },
    data: data,
    responseType: 'stream' as const,
  };

  try {
    const response = await axios(requestConfig);
    let fullText = "";

    return new Promise((resolve, reject) => {
      response.data.on('data', (chunk: Buffer) => {
        const lines = chunk.toString().split('\n');

        for (const line of lines) {
          if (line.startsWith('data: ')) {
            const data = line.slice(6);
            if (data === '[DONE]') {
              resolve(fullText);
              return;
            }

            try {
              const parsed = JSON.parse(data);
              const content = parsed.choices?.[0]?.delta?.content;
              if (content) {
                fullText += content;
                if (res) {
                  res.write(`data: ${JSON.stringify({ chunk: fullText, done: false })}\n\n`);
                }
              }
            } catch (e) {
              // 忽略解析错误
            }
          }
        }
      });

      response.data.on('end', () => {
        resolve(fullText);
      });

      response.data.on('error', (error: any) => {
        reject(error);
      });
    });
  } catch (error: any) {
    throw new Error(`翻译失败: ${error.response?.data?.error?.message || error.message}`);
  }
};

// 通义千问流式翻译
const translateWithTongyiStream = async (
  text: string,
  source: string,
  target: string,
  config?: any,
  res?: NextApiResponse
): Promise<string> => {
  const apiKey = config?.apiKey || process.env.TONGYI_API_KEY;
  const model = config?.model || "qwen-turbo";

  if (!apiKey) {
    throw new Error("通义千问 API 密钥未配置");
  }

  const data = {
    model: model,
    input: {
      messages: [
        {
          role: "user",
          content: `请将以下文本从${source}翻译成${target}语言：${text}`,
        },
      ],
    },
    parameters: {
      incremental_output: true,
    },
  };

  const requestConfig = {
    method: 'post',
    url: 'https://dashscope.aliyuncs.com/api/v1/services/aigc/text-generation/generation',
    headers: {
      'Authorization': `Bearer ${apiKey}`,
      'Content-Type': 'application/json',
      'Accept': 'text/event-stream',
      'X-DashScope-SSE': 'enable',
    },
    data: data,
    responseType: 'stream' as const,
  };

  try {
    const response = await axios(requestConfig);
    let fullText = "";

    return new Promise((resolve, reject) => {
      response.data.on('data', (chunk: Buffer) => {
        const lines = chunk.toString().split('\n');

        for (const line of lines) {
          if (line.startsWith('data:')) {
            const data = line.slice(5).trim();
            if (data === '[DONE]') {
              resolve(fullText);
              return;
            }

            try {
              const parsed = JSON.parse(data);
              const content = parsed.output?.text;
              if (content) {
                fullText = content;
                if (res) {
                  res.write(`data: ${JSON.stringify({ chunk: fullText, done: false })}\n\n`);
                }
              }
            } catch (e) {
              // 忽略解析错误
            }
          }
        }
      });

      response.data.on('end', () => {
        resolve(fullText);
      });

      response.data.on('error', (error: any) => {
        reject(error);
      });
    });
  } catch (error: any) {
    throw new Error(`翻译失败: ${error.response?.data?.error?.message || error.message}`);
  }
};

// 硅基流动流式翻译
const translateWithSiliconFlowStream = async (
  text: string,
  source: string,
  target: string,
  config?: any,
  res?: NextApiResponse
): Promise<string> => {
  const apiKey = config?.apiKey || process.env.SILICONFLOW_API_KEY;
  const model = config?.model || "Qwen/Qwen2.5-32B-Instruct";

  if (!apiKey) {
    throw new Error("硅基流动 API 密钥未配置");
  }

  const data = {
    model: model,
    messages: [
      {
        role: "user",
        content: `请将以下文本从${source}翻译成${target}语言：${text}`,
      },
    ],
    stream: true,
  };

  const requestConfig = {
    method: 'post',
    url: 'https://api.siliconflow.cn/v1/chat/completions',
    headers: {
      'Accept': 'text/event-stream',
      'Authorization': `Bearer ${apiKey}`,
      'Content-Type': 'application/json'
    },
    data: data,
    responseType: 'stream' as const,
  };

  try {
    const response = await axios(requestConfig);
    let fullText = "";

    return new Promise((resolve, reject) => {
      response.data.on('data', (chunk: Buffer) => {
        const lines = chunk.toString().split('\n');

        for (const line of lines) {
          if (line.startsWith('data: ')) {
            const data = line.slice(6);
            if (data === '[DONE]') {
              resolve(fullText);
              return;
            }

            try {
              const parsed = JSON.parse(data);
              const content = parsed.choices?.[0]?.delta?.content;
              if (content) {
                fullText += content;
                if (res) {
                  res.write(`data: ${JSON.stringify({ chunk: fullText, done: false })}\n\n`);
                }
              }
            } catch (e) {
              // 忽略解析错误
            }
          }
        }
      });

      response.data.on('end', () => {
        resolve(fullText);
      });

      response.data.on('error', (error: any) => {
        reject(error);
      });
    });
  } catch (error: any) {
    throw new Error(`翻译失败: ${error.response?.data?.error?.message || error.message}`);
  }
};

// 深度求索流式翻译
const translateWithDeepSeekStream = async (
  text: string,
  source: string,
  target: string,
  config?: any,
  res?: NextApiResponse
): Promise<string> => {
  const apiKey = config?.apiKey || process.env.DEEPSEEK_API_KEY;
  const model = config?.model || "deepseek-chat";

  if (!apiKey) {
    throw new Error("DeepSeek API 密钥未配置");
  }

  const data = {
    model: model,
    messages: [
      {
        role: "user",
        content: `请将以下文本从${source}翻译成${target}语言：${text}`,
      },
    ],
    stream: true,
  };

  const requestConfig = {
    method: 'post',
    url: 'https://api.deepseek.com/v1/chat/completions',
    headers: {
      'Accept': 'text/event-stream',
      'Authorization': `Bearer ${apiKey}`,
      'Content-Type': 'application/json'
    },
    data: data,
    responseType: 'stream' as const,
  };

  try {
    const response = await axios(requestConfig);
    let fullText = "";

    return new Promise((resolve, reject) => {
      response.data.on('data', (chunk: Buffer) => {
        const lines = chunk.toString().split('\n');

        for (const line of lines) {
          if (line.startsWith('data: ')) {
            const data = line.slice(6);
            if (data === '[DONE]') {
              resolve(fullText);
              return;
            }

            try {
              const parsed = JSON.parse(data);
              const content = parsed.choices?.[0]?.delta?.content;
              if (content) {
                fullText += content;
                if (res) {
                  res.write(`data: ${JSON.stringify({ chunk: fullText, done: false })}\n\n`);
                }
              }
            } catch (e) {
              // 忽略解析错误
            }
          }
        }
      });

      response.data.on('end', () => {
        resolve(fullText);
      });

      response.data.on('error', (error: any) => {
        reject(error);
      });
    });
  } catch (error: any) {
    throw new Error(`翻译失败: ${error.response?.data?.error?.message || error.message}`);
  }
};

// 自定义API流式翻译
const translateWithCustomAPIStream = async (
  text: string,
  source: string,
  target: string,
  customAPI: any,
  res?: NextApiResponse
): Promise<string> => {
  const data = {
    model: customAPI.model,
    messages: [
      {
        role: "user",
        content: `请将以下文本从${source}翻译成${target}语言：${text}`,
      },
    ],
    stream: true,
  };

  const requestConfig = {
    method: 'post',
    url: customAPI.endpoint,
    headers: {
      'Accept': 'text/event-stream',
      'Authorization': `Bearer ${customAPI.apiKey}`,
      'Content-Type': 'application/json'
    },
    data: data,
    responseType: 'stream' as const,
  };

  try {
    const response = await axios(requestConfig);
    let fullText = "";

    return new Promise((resolve, reject) => {
      response.data.on('data', (chunk: Buffer) => {
        const lines = chunk.toString().split('\n');

        for (const line of lines) {
          if (line.startsWith('data: ')) {
            const data = line.slice(6);
            if (data === '[DONE]') {
              resolve(fullText);
              return;
            }

            try {
              const parsed = JSON.parse(data);
              const content = parsed.choices?.[0]?.delta?.content;
              if (content) {
                fullText += content;
                if (res) {
                  res.write(`data: ${JSON.stringify({ chunk: fullText, done: false })}\n\n`);
                }
              }
            } catch (e) {
              // 忽略解析错误
            }
          }
        }
      });

      response.data.on('end', () => {
        resolve(fullText);
      });

      response.data.on('error', (error: any) => {
        reject(error);
      });
    });
  } catch (error: any) {
    throw new Error(`翻译失败: ${error.response?.data?.error?.message || error.message}`);
  }
};