"use client";

import {
  Box,
  VStack,
  Textarea,
  Select,
  Button,
  Spinner,
  HStack,
  Grid,
  GridItem,
  Flex,
  useToast,
  Text,
  Switch,
  FormControl,
  FormLabel,
} from "@chakra-ui/react";
import { useState, useRef } from "react";
import { useTranslationContext } from "../contexts/TranslationContext";
import { useConfigContext } from "../contexts/ConfigContext";
import { CopyIcon } from "@chakra-ui/icons";
import { IconButton } from "@chakra-ui/react";
import { v4 as uuidv4 } from 'uuid';

const StreamingTranslationInput = () => {
  const [text, setText] = useState<string>("");
  const [sourceLang, setSourceLang] = useState<string>("auto");
  const [targetLang, setTargetLang] = useState<string>("en");
  const [useStreaming, setUseStreaming] = useState<boolean>(true);
  const { services, customAPIs, addHistory } = useTranslationContext();
  const { configs } = useConfigContext();
  const [translations, setTranslations] = useState<{ service: string; text: string; name: string; isStreaming: boolean }[]>([]);
  const [loading, setLoading] = useState<boolean>(false);
  const toast = useToast();
  const eventSourcesRef = useRef<EventSource[]>([]);

  const handleTranslate = async () => {
    if (services.length === 0) {
      toast({
        title: "没有选择翻译服务",
        description: "请至少选择一个翻译服务后再进行翻译。",
        status: "warning",
        duration: 3000,
        isClosable: true,
        position: "top",
      });
      return;
    }

    setLoading(true);
    setTranslations([]);

    // 关闭之前的EventSource连接
    eventSourcesRef.current.forEach(es => es.close());
    eventSourcesRef.current = [];

    if (useStreaming) {
      // 流式翻译
      handleStreamingTranslation();
    } else {
      // 普通翻译
      handleNormalTranslation();
    }
  };

  const handleStreamingTranslation = () => {
    const initialTranslations = services.map(service => ({
      service,
      text: "",
      name: getServiceName(service),
      isStreaming: true,
    }));
    setTranslations(initialTranslations);

    services.forEach((service, index) => {
      const eventSource = new EventSource('/api/translate-stream', {
        // Note: EventSource doesn't support POST directly, we'll need to use fetch with SSE
      });

      // 使用fetch来发送POST请求并处理SSE响应
      fetch('/api/translate-stream', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          text,
          sourceLang: sourceLang === "auto" ? "" : sourceLang,
          targetLang,
          service,
          customAPIs,
          serviceConfigs: configs,
        }),
      }).then(response => {
        if (!response.body) {
          throw new Error('No response body');
        }

        const reader = response.body.getReader();
        const decoder = new TextDecoder();

        const readStream = () => {
          reader.read().then(({ done, value }) => {
            if (done) {
              setTranslations(prev => 
                prev.map((t, i) => 
                  i === index ? { ...t, isStreaming: false } : t
                )
              );
              
              // 添加到历史记录
              const finalTranslation = translations[index];
              if (finalTranslation && finalTranslation.text) {
                addHistory({
                  id: uuidv4(),
                  inputText: text,
                  sourceLang,
                  targetLang,
                  service: finalTranslation.service,
                  translatedText: finalTranslation.text,
                  timestamp: new Date().toISOString(),
                });
              }
              
              return;
            }

            const chunk = decoder.decode(value);
            const lines = chunk.split('\n');

            for (const line of lines) {
              if (line.startsWith('data: ')) {
                try {
                  const data = JSON.parse(line.slice(6));
                  
                  if (data.error) {
                    toast({
                      title: "翻译失败",
                      description: `${getServiceName(service)}: ${data.error}`,
                      status: "error",
                      duration: 3000,
                      isClosable: true,
                      position: "top",
                    });
                    return;
                  }

                  if (data.chunk) {
                    setTranslations(prev => 
                      prev.map((t, i) => 
                        i === index ? { ...t, text: data.chunk } : t
                      )
                    );
                  }
                } catch (e) {
                  // 忽略解析错误
                }
              }
            }

            readStream();
          }).catch(error => {
            console.error('Stream reading error:', error);
            toast({
              title: "翻译失败",
              description: `${getServiceName(service)}: 连接错误`,
              status: "error",
              duration: 3000,
              isClosable: true,
              position: "top",
            });
          });
        };

        readStream();
      }).catch(error => {
        console.error('Fetch error:', error);
        toast({
          title: "翻译失败",
          description: `${getServiceName(service)}: ${error.message}`,
          status: "error",
          duration: 3000,
          isClosable: true,
          position: "top",
        });
      });
    });

    // 检查是否所有翻译都完成
    const checkAllComplete = () => {
      const allComplete = translations.every(t => !t.isStreaming);
      if (allComplete && translations.length > 0) {
        setLoading(false);
        toast({
          title: "翻译完成",
          description: "所有翻译服务已完成翻译。",
          status: "success",
          duration: 3000,
          isClosable: true,
          position: "top",
        });
      }
    };

    // 设置定时器检查完成状态
    const interval = setInterval(() => {
      checkAllComplete();
      if (!loading) {
        clearInterval(interval);
      }
    }, 1000);
  };

  const handleNormalTranslation = async () => {
    try {
      const promises = services.map(service =>
        fetch('/api/translate', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            text,
            sourceLang: sourceLang === "auto" ? "" : sourceLang,
            targetLang,
            service,
            customAPIs,
            serviceConfigs: configs,
          }),
        }).then(res => res.json())
      );

      const results = await Promise.all(promises);
      const newTranslations = results.map((res, index) => ({
        service: services[index],
        text: res.translatedText || res.error || "翻译失败",
        name: getServiceName(services[index]),
        isStreaming: false,
      }));
      
      setTranslations(newTranslations);

      // 添加到历史记录
      newTranslations.forEach(t => {
        if (!t.text.includes("翻译失败")) {
          addHistory({
            id: uuidv4(),
            inputText: text,
            sourceLang,
            targetLang,
            service: t.service,
            translatedText: t.text,
            timestamp: new Date().toISOString(),
          });
        }
      });

      toast({
        title: "翻译成功",
        description: "您的文本已成功翻译。",
        status: "success",
        duration: 3000,
        isClosable: true,
        position: "top",
      });
    } catch (error: any) {
      console.error(error);
      toast({
        title: "翻译失败",
        description: "翻译失败，请稍后重试。",
        status: "error",
        duration: 3000,
        isClosable: true,
        position: "top",
      });
    } finally {
      setLoading(false);
    }
  };

  const handleCopy = (text: string) => {
    navigator.clipboard.writeText(text).then(() => {
      toast({
        title: "复制成功",
        description: "翻译文本已复制到剪贴板。",
        status: "success",
        duration: 3000,
        isClosable: true,
        position: "top",
      });
    }).catch(() => {
      toast({
        title: "复制失败",
        description: "无法复制文本，请手动复制。",
        status: "error",
        duration: 3000,
        isClosable: true,
        position: "top",
      });
    });
  };

  const getServiceName = (serviceId: string) => {
    const serviceMap: { [key: string]: string } = {
      google: "谷歌翻译",
      openai: "OpenAI",
      tongyi: "通义千问",
      deepl: "DeepL",
      siliconflow: "硅基流动",
      deepseek: "深度求索",
    };
    if (serviceId.startsWith("custom_")) {
      const customAPI = customAPIs.find(api => api.id === serviceId);
      return customAPI ? customAPI.name : serviceId;
    }
    return serviceMap[serviceId] || serviceId;
  };

  return (
    <VStack align="stretch" spacing={8} bg="gray.800" p={8} borderRadius="lg" boxShadow="xl">
      <Grid templateColumns="repeat(2, 1fr)" gap={6} width="100%">
        <GridItem>
          <Select
            value={sourceLang}
            onChange={(e) => setSourceLang(e.target.value)}
            bg="gray.700"
            borderColor="gray.600"
            transition="all 0.3s ease"
            _hover={{ borderColor: "brand.500" }}
            _focus={{ borderColor: "brand.500", boxShadow: "0 0 0 1px var(--chakra-colors-brand-500)" }}
          >
            <option value="auto">检测源语言</option>
            <option value="zh">中文</option>
            <option value="en">英语</option>
            <option value="ja">日语</option>
            <option value="fr">法语</option>
            <option value="ko">韩语</option>
            <option value="ru">俄语</option>
          </Select>
        </GridItem>
        <GridItem>
          <Select
            value={targetLang}
            onChange={(e) => setTargetLang(e.target.value)}
            bg="gray.700"
            borderColor="gray.600"
            transition="all 0.3s ease"
            _hover={{ borderColor: "brand.500" }}
            _focus={{ borderColor: "brand.500", boxShadow: "0 0 0 1px var(--chakra-colors-brand-500)" }}
          >
            <option value="en">英语</option>
            <option value="zh">中文</option>
            <option value="ja">日语</option>
            <option value="fr">法语</option>
            <option value="ko">韩语</option>
            <option value="ru">俄语</option>
          </Select>
        </GridItem>
      </Grid>

      <Textarea
        placeholder="请输入您需要翻译的文本"
        value={text}
        onChange={(e) => setText(e.target.value)}
        height="250px"
        resize="vertical"
        bg="gray.700"
        borderColor="gray.600"
        p={4}
      />

      <FormControl display="flex" alignItems="center">
        <FormLabel htmlFor="streaming-mode" mb="0">
          启用流式翻译
        </FormLabel>
        <Switch
          id="streaming-mode"
          isChecked={useStreaming}
          onChange={(e) => setUseStreaming(e.target.checked)}
          colorScheme="brand"
        />
      </FormControl>

      <Button
        colorScheme="brand"
        onClick={handleTranslate}
        isDisabled={!text || loading}
        width="100%"
        leftIcon={loading ? <Spinner size="sm" /> : undefined}
        _hover={{ transform: "translateY(-2px)", boxShadow: "lg" }}
        _active={{ transform: "translateY(1px)" }}
        transition="all 0.3s ease"
      >
        {loading ? "翻译中..." : "翻译"}
      </Button>

      <VStack align="stretch" spacing={4}>
        {translations.map((t, index) => (
          <Box
            key={index}
            p={4}
            bg="gray.700"
            borderRadius="md"
            boxShadow="md"
            position="relative"
          >
            <Flex justify="space-between" align="center" mb={2}>
              <HStack>
                <Text fontWeight="bold" color="brand.200">{t.name}</Text>
                {t.isStreaming && <Spinner size="sm" color="brand.500" />}
              </HStack>
              <IconButton
                aria-label={`复制来自${t.name}的翻译文本`}
                icon={<CopyIcon />}
                size="sm"
                colorScheme="brand"
                variant="ghost"
                onClick={() => handleCopy(t.text)}
                isDisabled={!t.text || t.isStreaming}
              />
            </Flex>
            <Text minHeight="20px">{t.text || (t.isStreaming ? "正在翻译..." : "")}</Text>
          </Box>
        ))}
      </VStack>
    </VStack>
  );
};

export default StreamingTranslationInput;
