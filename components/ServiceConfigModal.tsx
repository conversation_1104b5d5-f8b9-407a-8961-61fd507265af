"use client";

import {
  <PERSON><PERSON>,
  <PERSON>dal<PERSON><PERSON><PERSON>,
  <PERSON>dal<PERSON>ontent,
  <PERSON><PERSON><PERSON><PERSON>er,
  ModalCloseButton,
  ModalBody,
  <PERSON>dal<PERSON>ooter,
  Button,
  VStack,
  Input,
  FormControl,
  FormLabel,
  Switch,
  Select,
  Text,
  useToast,
  Divider,
  HStack,
  Badge
} from "@chakra-ui/react";
import { useState, useEffect } from "react";
import { useConfigContext } from "../contexts/ConfigContext";

interface ServiceConfigModalProps {
  isOpen: boolean;
  onClose: () => void;
  service: string;
}

const ServiceConfigModal = ({ isOpen, onClose, service }: ServiceConfigModalProps) => {
  const { getConfig, updateConfig, isConfigured } = useConfigContext();
  const toast = useToast();
  
  // 通用状态
  const [apiKey, setApiKey] = useState("");
  const [model, setModel] = useState("");
  const [baseURL, setBaseURL] = useState("");
  
  // DeepL 特有状态
  const [isPro, setIsPro] = useState(false);
  
  // Google 特有状态
  const [projectId, setProjectId] = useState("");
  const [keyFilename, setKeyFilename] = useState("");

  useEffect(() => {
    if (isOpen && service) {
      const config = getConfig(service as any);
      
      switch (service) {
        case 'openai':
          setApiKey(config.apiKey || "");
          setModel(config.model || "gpt-4o-mini");
          setBaseURL(config.baseURL || "https://api.openai.com/v1");
          break;
        case 'tongyi':
          setApiKey(config.apiKey || "");
          setModel(config.model || "qwen-turbo");
          break;
        case 'deepl':
          setApiKey(config.apiKey || "");
          setIsPro(config.isPro || false);
          break;
        case 'siliconflow':
          setApiKey(config.apiKey || "");
          setModel(config.model || "Qwen/Qwen2.5-32B-Instruct");
          break;
        case 'deepseek':
          setApiKey(config.apiKey || "");
          setModel(config.model || "deepseek-chat");
          break;
        case 'google':
          setProjectId(config.projectId || "");
          setKeyFilename(config.keyFilename || "");
          break;
      }
    }
  }, [isOpen, service, getConfig]);

  const handleSave = () => {
    try {
      switch (service) {
        case 'openai':
          updateConfig('openai', { apiKey, model, baseURL });
          break;
        case 'tongyi':
          updateConfig('tongyi', { apiKey, model });
          break;
        case 'deepl':
          updateConfig('deepl', { apiKey, isPro });
          break;
        case 'siliconflow':
          updateConfig('siliconflow', { apiKey, model });
          break;
        case 'deepseek':
          updateConfig('deepseek', { apiKey, model });
          break;
        case 'google':
          updateConfig('google', { projectId, keyFilename });
          break;
      }
      
      toast({
        title: "配置已保存",
        description: `${getServiceDisplayName(service)} 的配置已成功保存。`,
        status: "success",
        duration: 3000,
        isClosable: true,
      });
      
      onClose();
    } catch (error) {
      toast({
        title: "保存失败",
        description: "配置保存时发生错误，请重试。",
        status: "error",
        duration: 3000,
        isClosable: true,
      });
    }
  };

  const getServiceDisplayName = (serviceId: string) => {
    const names: { [key: string]: string } = {
      openai: "OpenAI",
      tongyi: "通义千问",
      deepl: "DeepL",
      siliconflow: "硅基流动",
      deepseek: "深度求索",
      google: "谷歌翻译"
    };
    return names[serviceId] || serviceId;
  };

  const renderConfigFields = () => {
    switch (service) {
      case 'openai':
        return (
          <VStack spacing={4}>
            <FormControl isRequired>
              <FormLabel>API Key</FormLabel>
              <Input
                type="password"
                value={apiKey}
                onChange={(e) => setApiKey(e.target.value)}
                placeholder="sk-..."
                bg="gray.700"
                borderColor="gray.600"
              />
            </FormControl>
            <FormControl>
              <FormLabel>Base URL</FormLabel>
              <Input
                value={baseURL}
                onChange={(e) => setBaseURL(e.target.value)}
                placeholder="https://api.openai.com/v1"
                bg="gray.700"
                borderColor="gray.600"
              />
              <Text fontSize="sm" color="gray.400" mt={1}>
                自定义API端点，支持OpenAI兼容接口
              </Text>
            </FormControl>
            <FormControl>
              <FormLabel>模型</FormLabel>
              <Select
                value={model}
                onChange={(e) => setModel(e.target.value)}
                bg="gray.700"
                borderColor="gray.600"
              >
                <option value="gpt-4o-mini">gpt-4o-mini</option>
                <option value="gpt-4o">gpt-4o</option>
                <option value="gpt-4-turbo">gpt-4-turbo</option>
                <option value="gpt-3.5-turbo">gpt-3.5-turbo</option>
              </Select>
            </FormControl>
          </VStack>
        );
        
      case 'tongyi':
        return (
          <VStack spacing={4}>
            <FormControl isRequired>
              <FormLabel>API Key</FormLabel>
              <Input
                type="password"
                value={apiKey}
                onChange={(e) => setApiKey(e.target.value)}
                placeholder="sk-..."
                bg="gray.700"
                borderColor="gray.600"
              />
            </FormControl>
            <FormControl>
              <FormLabel>模型</FormLabel>
              <Select
                value={model}
                onChange={(e) => setModel(e.target.value)}
                bg="gray.700"
                borderColor="gray.600"
              >
                <option value="qwen-turbo">qwen-turbo</option>
                <option value="qwen-plus">qwen-plus</option>
                <option value="qwen-max">qwen-max</option>
              </Select>
            </FormControl>
          </VStack>
        );
        
      case 'deepl':
        return (
          <VStack spacing={4}>
            <FormControl isRequired>
              <FormLabel>API Key</FormLabel>
              <Input
                type="password"
                value={apiKey}
                onChange={(e) => setApiKey(e.target.value)}
                placeholder="..."
                bg="gray.700"
                borderColor="gray.600"
              />
            </FormControl>
            <FormControl>
              <FormLabel>账户类型</FormLabel>
              <HStack>
                <Switch
                  isChecked={isPro}
                  onChange={(e) => setIsPro(e.target.checked)}
                />
                <Text>{isPro ? "专业版" : "免费版"}</Text>
                <Badge colorScheme={isPro ? "green" : "blue"}>
                  {isPro ? "api.deepl.com" : "api-free.deepl.com"}
                </Badge>
              </HStack>
            </FormControl>
          </VStack>
        );
        
      case 'siliconflow':
        return (
          <VStack spacing={4}>
            <FormControl isRequired>
              <FormLabel>API Key</FormLabel>
              <Input
                type="password"
                value={apiKey}
                onChange={(e) => setApiKey(e.target.value)}
                placeholder="sk-..."
                bg="gray.700"
                borderColor="gray.600"
              />
            </FormControl>
            <FormControl>
              <FormLabel>模型</FormLabel>
              <Select
                value={model}
                onChange={(e) => setModel(e.target.value)}
                bg="gray.700"
                borderColor="gray.600"
              >
                <option value="Qwen/Qwen2.5-32B-Instruct">Qwen/Qwen2.5-32B-Instruct</option>
                <option value="Qwen/Qwen2.5-14B-Instruct">Qwen/Qwen2.5-14B-Instruct</option>
                <option value="Qwen/Qwen2.5-7B-Instruct">Qwen/Qwen2.5-7B-Instruct</option>
              </Select>
            </FormControl>
          </VStack>
        );
        
      case 'deepseek':
        return (
          <VStack spacing={4}>
            <FormControl isRequired>
              <FormLabel>API Key</FormLabel>
              <Input
                type="password"
                value={apiKey}
                onChange={(e) => setApiKey(e.target.value)}
                placeholder="sk-..."
                bg="gray.700"
                borderColor="gray.600"
              />
            </FormControl>
            <FormControl>
              <FormLabel>模型</FormLabel>
              <Select
                value={model}
                onChange={(e) => setModel(e.target.value)}
                bg="gray.700"
                borderColor="gray.600"
              >
                <option value="deepseek-chat">deepseek-chat</option>
                <option value="deepseek-coder">deepseek-coder</option>
              </Select>
            </FormControl>
          </VStack>
        );
        
      case 'google':
        return (
          <VStack spacing={4}>
            <FormControl isRequired>
              <FormLabel>项目 ID</FormLabel>
              <Input
                value={projectId}
                onChange={(e) => setProjectId(e.target.value)}
                placeholder="your-project-id"
                bg="gray.700"
                borderColor="gray.600"
              />
            </FormControl>
            <FormControl isRequired>
              <FormLabel>密钥文件路径</FormLabel>
              <Input
                value={keyFilename}
                onChange={(e) => setKeyFilename(e.target.value)}
                placeholder="/path/to/service-account-key.json"
                bg="gray.700"
                borderColor="gray.600"
              />
              <Text fontSize="sm" color="gray.400" mt={1}>
                Google Cloud 服务账户密钥文件的完整路径
              </Text>
            </FormControl>
          </VStack>
        );
        
      default:
        return <Text>未知的服务类型</Text>;
    }
  };

  return (
    <Modal isOpen={isOpen} onClose={onClose} size="md">
      <ModalOverlay />
      <ModalContent bg="gray.800">
        <ModalHeader>
          <HStack>
            <Text>配置 {getServiceDisplayName(service)}</Text>
            {isConfigured(service as any) && (
              <Badge colorScheme="green">已配置</Badge>
            )}
          </HStack>
        </ModalHeader>
        <ModalCloseButton />
        <ModalBody>
          {renderConfigFields()}
        </ModalBody>
        <ModalFooter>
          <Button variant="ghost" mr={3} onClick={onClose}>
            取消
          </Button>
          <Button colorScheme="brand" onClick={handleSave}>
            保存配置
          </Button>
        </ModalFooter>
      </ModalContent>
    </Modal>
  );
};

export default ServiceConfigModal;
